import * as React from "react";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { formatDuration, getServiceById } from "@/services/serviceService";
import { getCurrencySymbol } from "@/services/currencyService";
import { getOrderById } from "@/services/ordersServices";
interface GlobalCardProps {
  orderId: string; //
  border?: string;
}

export function SimplePaymentServiceCard({ orderId, border }: GlobalCardProps) {
  const [order, setOrder] = React.useState<any | null>(null);
  const [orderCustomizations, setOrderCustomizations] = React.useState<any | null>(null);

  const [loading, setLoading] = React.useState<boolean>(true);
  const [error, setError] = React.useState<string | null>(null);

  // console.log({ orderId });

  React.useEffect(() => {
    async function fetchOrder() {
      try {
        setLoading(true);
        const res = await getOrderById(orderId);
        console.log("Order data:", res);
        setOrderCustomizations(res?.order?.selectedCustomizations);

        const resps = await getServiceById(res?.order?.serviceId || "");
        console.log("Service data:", resps);
        console.log("Service customizations_array:", resps?.service?.customizations_array);
        console.log("Selected customizations:", res?.order?.selectedCustomizations);

        setOrder(resps.service);
      } catch (err: any) {
        console.error("Error fetching order/service:", err);
        setError(err.message || "Failed to load order");
      } finally {
        setLoading(false);
      }
    }

    if (orderId) {
      fetchOrder();
    }
  }, [orderId]);

  if (loading) {
    return <p>Loading order...</p>;
  }

  if (error) {
    return <p className="text-red-500">Error: {error}</p>;
  }

  if (!order) {
    return <p>No order found</p>;
  }

  // console.log(order);

  return (
    <>


      <div className="w-full mt-0 p-0 border-b-2 border-[#7C7C7C] pb-4">
        <div>
          <p className="text-base font-bold text-primary my-2">
            {order?.title || "Untitled Service"}
          </p>
        </div>
        <div className="flex justify-between">
          <p className="text-subtitle">Approximate time</p>
          <p className="text-lg font-bold text-subtitle">
            {formatDuration(order?.duration || "0")}
          </p>
        </div>
        <div className="flex justify-between">
          <p className="text-subtitle">Service subtotal</p>
          <p className="text-lg text-subtitle">
            {getCurrencySymbol(order)}
            {parseFloat(order?.price || "0").toFixed(2)}
          </p>
        </div>
      </div>
      {orderCustomizations &&
        orderCustomizations.length > 0 &&
        order?.customizations_array &&
        orderCustomizations.map((customizationId: string, index: number) => {
          const customization = order?.customizations_array?.find(
            (c: any) => c.id === customizationId
          );
          if (!customization) {
            console.log(`Customization not found for ID: ${customizationId}`);
            return null;
          }

          return (
            <div key={customizationId} className="flex justify-between">
              <div
                className={
                  (orderCustomizations ?? []).length - 1 === index
                    ? "flex gap-3 w-full border-b-2 border-[#7C7C7C]"
                    : "flex gap-3 w-full "
                }
              >
                <p className="text-sm font-bold text-primary my-2 line-clamp-1 w-10">
                  #{index + 1}
                </p>

                <div className="w-full mt-0 p-0  pb-4">
                  <div>
                    <p className="text-sm font-bold text-primary my-2 line-clamp-1">
                      {customization.title}
                    </p>

                    {/* </Link> */}
                  </div>
                  <div className="flex justify-between ">
                    <p className="text-subtitle">Approximate time</p>
                    <p className="text-lg font-bold text-subtitle">
                      {formatDuration(customization.duration || "0")}
                    </p>
                  </div>
                  <div className="flex justify-between ">
                    <p className="text-subtitle">Customization subtotal</p>
                    <p className="text-lg text-subtitle">
                      {getCurrencySymbol(order.currency ?? "gbp")}
                      {(parseFloat(customization.price || "0") / (1 - 0.16)).toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          );
        })}


          {/* Clean Order Summary */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Order Summary</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Order ID</span>
              <span className="font-mono text-sm text-gray-800">{orderId || "N/A"}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Amount</span>
              <span className="text-2xl font-bold text-gray-900">
                {currency.toUpperCase()} {(amount / 100).toFixed(2)}
              </span>
            </div>
          </div>
        </div>
    </>
  );
}
