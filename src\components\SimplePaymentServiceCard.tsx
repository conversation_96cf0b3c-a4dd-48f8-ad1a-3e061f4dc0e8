import * as React from "react";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { formatDuration, getServiceById } from "@/services/serviceService";
import { getCurrencySymbol } from "@/services/currencyService";
import { getOrderById } from "@/services/ordersServices";
interface GlobalCardProps {
  orderId: string; //
  border?: string;
}

export function SimplePaymentServiceCard({ orderId, border }: GlobalCardProps) {
  const [order, setOrder] = React.useState<any | null>(null);
  const [orderCustomizations, setOrderCustomizations] = React.useState<any | null>(null);

  const [loading, setLoading] = React.useState<boolean>(true);
  const [error, setError] = React.useState<string | null>(null);

  // console.log({ orderId });

  React.useEffect(() => {
    async function fetchOrder() {
      try {
        setLoading(true);
        const res = await getOrderById(orderId);
        console.log({ res });
        setOrderCustomizations(res?.order?.selectedCustomizations);
        const resps = await getServiceById(res?.order?.serviceId || "");
        console.log(resps);

        setOrder(resps.service);
      } catch (err: any) {
        setError(err.message || "Failed to load order");
      } finally {
        setLoading(false);
      }
    }

    if (orderId) {
      fetchOrder();
    }
  }, [orderId]);

  if (loading) {
    return <p>Loading order...</p>;
  }

  if (error) {
    return <p className="text-red-500">Error: {error}</p>;
  }

  if (!order) {
    return <p>No order found</p>;
  }

  // console.log(order);

  return (
    <>
      <Card
        className="rounded-md border-l-[10px] shadow-none min-h-[150px] max-h-[150px] w-full"
        style={{
          borderLeftColor: border,
          borderTop: "1px solid #E5E5E5",
          borderRight: "1px solid #E5E5E5",
          borderBottom: "1px solid #E5E5E5",
        }}
      >
        <CardHeader className="px-4 py-3">
          <CardTitle>
            <p className="text-lg font-bold line-clamp-1 max-md:text-base">{order.title ?? ""}</p>
            <div className="row justify-between">
              <p className="font-normal text-base max-md:text-sm">
                {formatDuration(order.duration, {
                  dayLabel: "day",
                  hourLabel: "hour",
                  handlePlural: true,
                })}
              </p>
              <p style={{ color: border }} className="font-[600] text-base max-md:text-sm">
                {getCurrencySymbol(order.currency ?? "gbp")}
                {order?.price ? (order.price / (1 - 0.16)).toFixed(2) : "0"}
              </p>
            </div>
          </CardTitle>
          <CardDescription className="flex flex-row items-end justify-start">
            <div className="text-subtitle text-base max-md:text-sm line-clamp-3">
              {order.description ?? ""}
            </div>
          </CardDescription>
        </CardHeader>
      </Card>
      {orderCustomizations?.selectedCustomizations?.map(
        (customizationId: string, index: number) => {
          const customization = order?.serviceDetails?.customizations?.find(
            (c: any) => c.id === customizationId
          );
          if (!customization) return null;

          return (
            <div key={customizationId} className="flex justify-between">
              <div
                className={
                  (order?.selectedCustomizations ?? []).length - 1 === index
                    ? "flex gap-3 w-full border-b-2 border-[#7C7C7C]"
                    : "flex gap-3 w-full "
                }
              >
                <p className="text-sm font-bold text-primary my-2 line-clamp-1 w-10">
                  #{index + 1}
                </p>

                <div className="w-full mt-0 p-0  pb-4">
                  <div>
                    <p className="text-sm font-bold text-primary my-2 line-clamp-1">
                      {customization.title}
                    </p>

                    {/* </Link> */}
                  </div>
                  <div className="flex justify-between ">
                    <p className="text-subtitle">Approximate time</p>
                    <p className="text-lg font-bold text-subtitle">
                      {formatDuration(customization.duration || "0")}
                    </p>
                  </div>
                  <div className="flex justify-between ">
                    <p className="text-subtitle">Customization subtotal</p>
                    <p className="text-lg text-subtitle">
                      {getCurrencySymbol(order)}
                      {parseFloat(customization.price || "0").toFixed(2)}
                    </p>
                  </div>
                  {/* <div className="flex gap-3 mt-2">
                            <button
                              className="btn-xs border-primary btn w-full py-2 border rounded-full text-center"
                              onClick={() => {
                                const basketItem = createBasketItemFromOrder(order);
                                setSelectedItem(basketItem);
                                setIsEditOpen(true);
                              }}
                            >
                              Edit
                            </button>
                            <button
                              className="btn-xs btn w-full py-2 border rounded-full text-center hover:text-white"
                              onClick={() => {
                                handleDeleteCustomization(order.id, customizationId);
                              }}
                            >
                              Delete
                            </button>
                          </div> */}
                </div>
              </div>
            </div>
          );
        }
      )}
    </>
  );
}
